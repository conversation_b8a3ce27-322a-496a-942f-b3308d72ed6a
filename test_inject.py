from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
import time
import asyncio
import httpx

async def solve_with_botsforge_service(website_url, website_key):
    """
    Use BotsForge visual recognition service to solve Turnstile
    """
    api_key = "pirateship-turnstile-solver-2024"
    base_url = "http://localhost:5033"
    
    print(f"🤖 Using BotsForge service to solve Turnstile for {website_url}")
    
    try:
        async with httpx.AsyncClient(timeout=120.0) as client:
            # Create task
            create_payload = {
                "clientKey": api_key,
                "task": {
                    "type": "AntiTurnstileTaskProxyLess",
                    "websiteURL": website_url,
                    "websiteKey": website_key
                }
            }
            
            print("📤 Creating Turnstile solving task...")
            response = await client.post(f'{base_url}/createTask', json=create_payload)
            task_data = response.json()
            
            if task_data.get('status') != 'idle':
                print(f"❌ Failed to create task: {task_data}")
                return None
            
            task_id = task_data['taskId']
            print(f"✅ Task created with ID: {task_id}")
            
            # Poll for result
            print("⏳ Waiting for BotsForge to solve Turnstile...")
            for attempt in range(120):  # 2 minute timeout
                await asyncio.sleep(1)
                
                result_payload = {
                    "clientKey": api_key,
                    "taskId": task_id
                }
                
                response = await client.post(f'{base_url}/getTaskResult', json=result_payload)
                result = response.json()
                
                if result.get('status') == 'ready':
                    token = result['solution']['token']
                    print(f"🎉 BotsForge solved Turnstile! Token: {token[:20]}...")
                    return token
                elif result.get('status') == 'error':
                    print(f"❌ BotsForge error: {result.get('errorDescription')}")
                    return None
                
                # Progress indicator
                if attempt % 10 == 0 and attempt > 0:
                    print(f"⏳ Still waiting... ({attempt}s elapsed)")
            
            print("⏰ BotsForge service timeout")
            return None
            
    except Exception as e:
        print(f"❌ Error communicating with BotsForge service: {e}")
        return None

# Load the inject.js file content
with open("inject.js", "r", encoding="utf-8") as f:
    inject_js_content = f.read()

    try:
        # Solve the Turnstile CAPTCHA
        # Extract Turnstile parameters from current page
        options = webdriver.ChromeOptions()
        options.binary_location = "C://Program Files/BraveSoftware/Brave-Browser/Application/brave.exe"
        with webdriver.Chrome(options=options) as driver:
            turnstile_data = driver.execute_script("""
            const container = document.getElementById('cf-turnstile');
            if (container) {
                // Try multiple ways to get the site key
                let siteKey = container.getAttribute('data-sitekey') ||
                            container.querySelector('[data-sitekey]')?.getAttribute('data-sitekey');
                let iframeSrc = 'not found';

                // If not found, look in the shadow DOM
                if (!siteKey) {
                    const shadowHost = container.querySelector('div');
                    if (shadowHost && shadowHost.shadowRoot) {
                        const iframe = shadowHost.shadowRoot.querySelector('iframe');
                        if (iframe && iframe.src) {
                            iframeSrc = iframe.src;
                            console.log('Iframe src:', iframe.src);

                            // Extract site key from iframe src - Cloudflare Turnstile pattern
                            // Pattern: /rcv/[id]/[sitekey]/
                            const match = iframe.src.match(/\\/rcv\\/[^\\/]+\\/([^\\/]+)\\//);
                            if (match) {
                                siteKey = match[1];
                                console.log('Extracted site key:', siteKey);
                            } else {
                                // Fallback: try other patterns
                                const fallbackMatch = iframe.src.match(/[?&]sitekey=([^&]+)/);
                                if (fallbackMatch) siteKey = fallbackMatch[1];
                            }
                        }
                    }
                }

                return {
                    websiteURL: window.location.href,
                    websiteKey: siteKey,
                    containerFound: true,
                    iframeSrc: iframeSrc
                };
            }
            return {containerFound: false};
            """)
            
            if not turnstile_data.get('containerFound'):
                print("❌ No Turnstile container found on page")
            
            if not turnstile_data.get('websiteKey'):
                print("❌ Could not extract Turnstile site key")
            
            print(f"� Extracted Turnstile data: {turnstile_data['websiteKey']}")
            
            # Solve with BotsForge service
            token = solve_with_botsforge_service(
                turnstile_data['websiteURL'], 
                turnstile_data['websiteKey']
            )
            print("CAPTCHA Solved Token:", token)

            # Start Selenium WebDriver
            driver = webdriver.Chrome()

            print(inject_js_content)

            # Inject JavaScript from local file
            driver.execute_cdp_cmd(
                "Page.addScriptToEvaluateOnNewDocument",
                {"source": inject_js_content}
            )


            # Navigate to the target page
            driver.get("https://dashboard.capsolver.com/passport/login")

            # Wait for the CAPTCHA response input
            WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.NAME, "cf-turnstile-response")))

            # Wait for the email and password fields to appear
            WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.NAME, "email")))
            WebDriverWait(driver, 10).until(EC.presence_of_element_located((By.NAME, "password")))

            # Enter email and password
            email = ""  # Replace with actual email
            password = ""  # Replace with actual password
            driver.find_element(By.NAME, "email").send_keys(email)
            driver.find_element(By.NAME, "password").send_keys(password)


            # Inject the CAPTCHA token
            driver.execute_script("""
                const captchaInput = document.querySelector('[name="cf-turnstile-response"]');
                if (captchaInput) {
                    captchaInput.value = arguments[0];
                    captchaInput.dispatchEvent(new Event("input", { bubbles: true }));
                    captchaInput.dispatchEvent(new Event("change", { bubbles: true }));
                }
                if (window.turnstile && typeof window.tsCallback === "function") {
                    window.tsCallback(arguments[0]);
                }
            """, token)

            # Wait for the submit button to be enabled
            WebDriverWait(driver, 10).until(lambda d: d.find_element(By.CSS_SELECTOR, 'form button[type="submit"]').is_enabled())

            # Click the submit button
            driver.find_element(By.CSS_SELECTOR, 'form button[type="submit"]').click()

            # Optional: Wait for the next page to load or perform further actions
            time.sleep(120)

    finally:
        # Close the browser
        driver.quit()