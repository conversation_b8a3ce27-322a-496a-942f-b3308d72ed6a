import time
from selenium_driverless import webdriver
from selenium_driverless.types.by import By
import asyncio
import httpx

# Test credentials
email = "<EMAIL>"
password = "sacramento209%%"
element_timeout = 15

async def solve_with_botsforge_service(website_url, website_key):
    """
    Use BotsForge visual recognition service to solve Turnstile
    """
    api_key = "pirateship-turnstile-solver-2024"
    base_url = "http://localhost:5033"
    
    print(f"🤖 Using BotsForge service to solve Turnstile for {website_url}")
    
    try:
        async with httpx.AsyncClient(timeout=120.0) as client:
            # Create task
            create_payload = {
                "clientKey": api_key,
                "task": {
                    "type": "AntiTurnstileTaskProxyLess",
                    "websiteURL": website_url,
                    "websiteKey": website_key
                }
            }
            
            print("📤 Creating Turnstile solving task...")
            response = await client.post(f'{base_url}/createTask', json=create_payload)
            task_data = response.json()
            
            if task_data.get('status') != 'idle':
                print(f"❌ Failed to create task: {task_data}")
                return None
            
            task_id = task_data['taskId']
            print(f"✅ Task created with ID: {task_id}")
            
            # Poll for result
            print("⏳ Waiting for BotsForge to solve Turnstile...")
            for attempt in range(120):  # 2 minute timeout
                await asyncio.sleep(1)
                
                result_payload = {
                    "clientKey": api_key,
                    "taskId": task_id
                }
                
                response = await client.post(f'{base_url}/getTaskResult', json=result_payload)
                result = response.json()
                
                if result.get('status') == 'ready':
                    token = result['solution']['token']
                    print(f"🎉 BotsForge solved Turnstile! Token: {token[:20]}...")
                    return token
                elif result.get('status') == 'error':
                    print(f"❌ BotsForge error: {result.get('errorDescription')}")
                    return None
                
                # Progress indicator
                if attempt % 10 == 0 and attempt > 0:
                    print(f"⏳ Still waiting... ({attempt}s elapsed)")
            
            print("⏰ BotsForge service timeout")
            return None
            
    except Exception as e:
        print(f"❌ Error communicating with BotsForge service: {e}")
        return None

async def cloudflare_turnstile_solver_legacy(driver):
    """
    Legacy CDP shadow DOM approach (fast but less reliable)
    """
    print("🔧 Trying legacy CDP shadow DOM approach...")
    
    try:
        # Check if login button is already enabled
        login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=5)
        is_disabled = await login_button.get_dom_attribute('disabled')
        
        if is_disabled is None:
            print("✅ Login button already enabled - no Turnstile required")
            return True
        
        # Look for Turnstile container
        turnstile_container = await driver.find_element(By.ID, "cf-turnstile", timeout=10)
        print("🎯 Found Turnstile container")
        
        # Wait for Turnstile to load
        await driver.sleep(5)
        
        # Try shadow DOM access
        try:
            shadow_host = await turnstile_container.find_element(By.CSS_SELECTOR, "div", timeout=5)
            shadow_root = await driver.execute_script("return arguments[0].shadowRoot", shadow_host)
            
            if shadow_root:
                print("📱 Accessed shadow root")
                iframe = await driver.execute_script("return arguments[0].querySelector('iframe')", shadow_root)
                
                if iframe:
                    print("🖼️ Found iframe in shadow DOM")
                    # Try gentle click on container
                    await turnstile_container.click()
                    print("👆 Clicked Turnstile container")
                    
        except Exception as shadow_error:
            print(f"⚠️ Shadow DOM access failed: {shadow_error}")
            await turnstile_container.click()
        
        # Wait and check if successful
        await driver.sleep(8)
        
        for attempt in range(10):
            is_disabled = await login_button.get_dom_attribute('disabled')
            if is_disabled is None:
                print(f"✅ Legacy method successful after {attempt + 1} attempts!")
                return True
            await driver.sleep(2)
        
        print("❌ Legacy method failed")
        return False
        
    except Exception as e:
        print(f"❌ Legacy method error: {e}")
        return False

async def cloudflare_turnstile_solver(driver):
    """
    Hybrid Cloudflare Turnstile solver with BotsForge fallback.
    
    Two-tier approach:
    1. Fast CDP shadow DOM method (legacy)
    2. BotsForge visual recognition service (reliable fallback)
    """
    print("🔒 Starting hybrid Cloudflare Turnstile verification...")
    
    # Tier 1: Try legacy method first (faster)
    print("🚀 Tier 1: Attempting fast legacy method...")
    if await cloudflare_turnstile_solver_legacy(driver):
        print("✅ Tier 1 successful - Turnstile solved with legacy method!")
        return True
    
    # Tier 2: Fallback to BotsForge service
    print("🔄 Tier 1 failed - falling back to BotsForge visual recognition...")
    
    try:
        # Extract Turnstile parameters from current page
        turnstile_data = await driver.execute_script("""
        const container = document.getElementById('cf-turnstile');
        if (container) {
            // Try multiple ways to get the site key
            let siteKey = container.getAttribute('data-sitekey') ||
                         container.querySelector('[data-sitekey]')?.getAttribute('data-sitekey');
            let iframeSrc = 'not found';

            // If not found, look in the shadow DOM
            if (!siteKey) {
                const shadowHost = container.querySelector('div');
                if (shadowHost && shadowHost.shadowRoot) {
                    const iframe = shadowHost.shadowRoot.querySelector('iframe');
                    if (iframe && iframe.src) {
                        iframeSrc = iframe.src;
                        console.log('Iframe src:', iframe.src);

                        // Extract site key from iframe src - Cloudflare Turnstile pattern
                        // Pattern: /rcv/[id]/[sitekey]/
                        const match = iframe.src.match(/\\/rcv\\/[^\\/]+\\/([^\\/]+)\\//);
                        if (match) {
                            siteKey = match[1];
                            console.log('Extracted site key:', siteKey);
                        } else {
                            // Fallback: try other patterns
                            const fallbackMatch = iframe.src.match(/[?&]sitekey=([^&]+)/);
                            if (fallbackMatch) siteKey = fallbackMatch[1];
                        }
                    }
                }
            }

            return {
                websiteURL: window.location.href,
                websiteKey: siteKey,
                containerFound: true,
                iframeSrc: iframeSrc
            };
        }
        return {containerFound: false};
        """)
        
        if not turnstile_data.get('containerFound'):
            print("❌ No Turnstile container found on page")
            return False
        
        if not turnstile_data.get('websiteKey'):
            print("❌ Could not extract Turnstile site key")
            return False
        
        print(f"� Extracted Turnstile data: {turnstile_data['websiteKey']}")
        
        # Solve with BotsForge service
        token = await solve_with_botsforge_service(
            turnstile_data['websiteURL'], 
            turnstile_data['websiteKey']
        )
        
        if token:
            # Inject token using CapSolver-style approach
            print("💉 Injecting token using enhanced CapSolver methodology...")
            injection_result = await driver.execute_script(f"""
            // Enhanced token injection based on CapSolver approach
            let injected = false;
            let callbackCalled = false;
            let message = '';

            // Method 1: Look for cf-turnstile-response input and inject token
            let input = document.querySelector('input[name="cf-turnstile-response"]');
            if (input) {{
                input.value = '{token}';
                input.dispatchEvent(new Event('change', {{bubbles: true}}));
                input.dispatchEvent(new Event('input', {{bubbles: true}}));
                injected = true;
                message += 'Found and updated cf-turnstile-response input; ';
            }}

            // Method 2: Create the input if it doesn't exist
            if (!input) {{
                input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'cf-turnstile-response';
                input.value = '{token}';

                // Try to add it to the form
                const form = document.querySelector('form');
                if (form) {{
                    form.appendChild(input);
                    injected = true;
                    message += 'Created cf-turnstile-response input in form; ';
                }} else {{
                    document.body.appendChild(input);
                    injected = true;
                    message += 'Created cf-turnstile-response input in body; ';
                }}
            }}

            // Method 3: CRITICAL - Call the captured Turnstile callback (CapSolver approach)
            if (window.tsCallback && typeof window.tsCallback === 'function') {{
                try {{
                    console.log('🎯 Calling captured Turnstile callback with token');
                    window.tsCallback('{token}');
                    callbackCalled = true;
                    message += 'Called captured Turnstile callback; ';
                }} catch (e) {{
                    message += 'Callback call failed: ' + e.message + '; ';
                }}
            }} else {{
                // Try to find callback in Turnstile widget if not captured during render
                try {{
                    const container = document.getElementById('cf-turnstile');
                    if (container && window.turnstile) {{
                        // Look for widget data that might contain callback
                        const widgets = window.turnstile._widgets || [];
                        for (let widget of widgets) {{
                            if (widget.callback && typeof widget.callback === 'function') {{
                                console.log('🔄 Found widget callback, calling it');
                                widget.callback('{token}');
                                callbackCalled = true;
                                message += 'Called widget callback; ';
                                break;
                            }}
                        }}
                    }}
                }} catch (e) {{
                    message += 'Widget callback search failed: ' + e.message + '; ';
                }}

                if (!callbackCalled) {{
                    message += 'No captured callback found; ';
                }}
            }}

            // Method 4: Fallback - try to find and call any Turnstile callback
            if (!callbackCalled) {{
                try {{
                    // Look for callback in Turnstile widget data
                    const container = document.getElementById('cf-turnstile');
                    if (container && window.turnstile) {{
                        // Try to trigger success event
                        const event = new CustomEvent('cf-turnstile-callback', {{
                            detail: {{ token: '{token}' }}
                        }});
                        container.dispatchEvent(event);
                        message += 'Triggered fallback turnstile event; ';
                    }}
                }} catch (e) {{
                    message += 'Fallback callback failed: ' + e.message + '; ';
                }}
            }}

            // Method 5: Set global variables that Turnstile might check
            window.turnstileToken = '{token}';
            window.cfTurnstileResponse = '{token}';

            return {{
                success: injected,
                callbackCalled: callbackCalled,
                message: message || 'No injection methods succeeded',
                tokenLength: '{token}'.length,
                capturedCallback: !!window.tsCallback
            }};
            """)
            
            print(f"💉 Enhanced token injection result: {injection_result}")

            # Wait for page to process the token and callback
            await driver.sleep(5)  # Increased wait time for callback processing

            # Check if login button is now enabled
            login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=5)
            is_disabled = await login_button.get_dom_attribute('disabled')

            if is_disabled is None:
                callback_status = "with callback" if injection_result.get('callbackCalled') else "without callback"
                print(f"✅ Tier 2 successful - BotsForge solved Turnstile {callback_status}!")
                return True
            else:
                callback_status = "called" if injection_result.get('callbackCalled') else "NOT called"
                print(f"⚠️ Token injected and callback {callback_status}, but login button still disabled")
                print(f"🔍 Debug info: {injection_result}")
                return False
        else:
            print("❌ BotsForge failed to solve Turnstile")
            return False
            
    except Exception as e:
        print(f"❌ Tier 2 error: {e}")
        return False

async def test_hybrid_login():
    """Test the hybrid Turnstile solution with BotsForge fallback"""
    options = webdriver.ChromeOptions()
    options.binary_location = "C://Program Files/BraveSoftware/Brave-Browser/Application/brave.exe"

    async with webdriver.Chrome(options=options) as driver:
        print("🚀 Starting hybrid Turnstile login test...")

        # Force all shadow DOMs to be open instead of closed
        await driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {'source': """
        Element.prototype._attachShadow = Element.prototype.attachShadow;
        Element.prototype.attachShadow = function () {
            return this._attachShadow( { mode: "open" } );
        };
        """})
        print("🔧 Configured shadow DOM override")

        # Inject CapSolver-style Turnstile callback capture script
        await driver.execute_cdp_cmd('Page.addScriptToEvaluateOnNewDocument', {'source': """
        const turnstileCallbackCapture = setInterval(() => {
            if (window.turnstile) {
                clearInterval(turnstileCallbackCapture);
                console.log('🎯 Turnstile detected, capturing callback...');

                // Store the original render method
                const originalRender = window.turnstile.render;

                // Override the render method to capture callback
                window.turnstile.render = (container, options) => {
                    console.log('📋 Turnstile render called with options:', options);

                    // Store the callback function for later use
                    if (options && options.callback) {
                        window.tsCallback = options.callback;
                        console.log('✅ Turnstile callback captured');
                    }

                    // Store other important parameters
                    window.tsSiteKey = options?.sitekey || options?.['site-key'];
                    window.tsAction = options?.action;
                    window.tsData = options?.cData;

                    // Call the original render function
                    const result = originalRender.call(window.turnstile, container, options);
                    console.log('🔄 Original Turnstile render completed');
                    return result;
                };

                console.log('🔧 Turnstile render method overridden');
            }
        }, 100);

        // Cleanup after 30 seconds if Turnstile never loads
        setTimeout(() => {
            clearInterval(turnstileCallbackCapture);
        }, 30000);
        """})
        print("🎯 Configured CapSolver-style Turnstile callback capture")
        
        # Navigate to PirateShip login page
        await driver.get('https://ship.pirateship.com/')
        print("🌐 Navigated to PirateShip")

        # Wait for Turnstile to load and capture callback
        print("⏳ Waiting for Turnstile to load...")
        for attempt in range(15):  # Wait up to 15 seconds
            await driver.sleep(1)
            callback_status = await driver.execute_script("""
            return {
                turnstileLoaded: !!window.turnstile,
                callbackCaptured: !!window.tsCallback,
                siteKeyCaptured: !!window.tsSiteKey,
                renderOverridden: window.turnstile && window.turnstile.render.toString().includes('tsCallback'),
                callbackType: typeof window.tsCallback
            };
            """)

            if callback_status.get('turnstileLoaded'):
                print(f"✅ Turnstile loaded after {attempt + 1} seconds")
                break

        # If Turnstile loaded but callback not captured, try manual capture
        if callback_status.get('turnstileLoaded') and not callback_status.get('callbackCaptured'):
            print("🔄 Turnstile loaded but callback not captured, attempting manual capture...")
            manual_capture_result = await driver.execute_script("""
            if (window.turnstile && !window.tsCallback) {
                // Store the original render method
                const originalRender = window.turnstile.render;

                // Override the render method to capture callback
                window.turnstile.render = (container, options) => {
                    console.log('📋 Manual Turnstile render called with options:', options);

                    // Store the callback function for later use
                    if (options && options.callback) {
                        window.tsCallback = options.callback;
                        console.log('✅ Manual Turnstile callback captured');
                    }

                    // Store other important parameters
                    window.tsSiteKey = options?.sitekey || options?.['site-key'];
                    window.tsAction = options?.action;
                    window.tsData = options?.cData;

                    // Call the original render function
                    const result = originalRender.call(window.turnstile, container, options);
                    console.log('🔄 Manual Turnstile render completed');
                    return result;
                };

                return { success: true, message: 'Manual override applied' };
            }
            return { success: false, message: 'Turnstile not available or callback already captured' };
            """)
            print(f"🔧 Manual capture result: {manual_capture_result}")

        # Final status check
        final_status = await driver.execute_script("""
        return {
            turnstileLoaded: !!window.turnstile,
            callbackCaptured: !!window.tsCallback,
            siteKeyCaptured: !!window.tsSiteKey,
            renderOverridden: window.turnstile && window.turnstile.render.toString().includes('tsCallback'),
            callbackType: typeof window.tsCallback
        };
        """)

        print(f"🔍 Final Turnstile callback capture status:")
        print(f"   - Turnstile loaded: {final_status.get('turnstileLoaded', False)}")
        print(f"   - Callback captured: {final_status.get('callbackCaptured', False)}")
        print(f"   - Site key captured: {final_status.get('siteKeyCaptured', False)}")
        print(f"   - Render overridden: {final_status.get('renderOverridden', False)}")
        print(f"   - Callback type: {final_status.get('callbackType', 'undefined')}")
        
        # Enter credentials
        email_input = await driver.find_element(By.CSS_SELECTOR, "input[name='email']", timeout=element_timeout)
        await email_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await email_input.send_keys(email)
        print("📧 Entered email")
        
        password_input = await driver.find_element(By.CSS_SELECTOR, "input[name='password']", timeout=element_timeout)
        await password_input.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        await password_input.send_keys(password)
        print("🔑 Entered password")
        
        await driver.sleep(2)
        
        # Test hybrid Turnstile solver
        turnstile_result = await cloudflare_turnstile_solver(driver)
        print(f"🎯 Hybrid Turnstile result: {turnstile_result}")
        
        # Click login button
        login_button = await driver.find_element(By.CSS_SELECTOR, "button[type='submit']", timeout=element_timeout)
        await login_button.click(spread_a=0, spread_b=0, bias_a=0, bias_b=0,border=0,scroll_to=False,move_to=False)
        print("🚀 Clicked login button")
        
        # Wait for login to complete
        await driver.sleep(10)
        
        # Check if login was successful
        try:
            current_url = await driver.current_url
            print(f"📍 Current URL after login: {current_url}")
            
            # Look for login form still being present
            try:
                await driver.find_element(By.CSS_SELECTOR, "input[name='email']", timeout=3)
                print("❌ HYBRID TEST FAILED - still on login page")
                return False
            except:
                print("✅ HYBRID TEST SUCCESSFUL - moved past login page!")
                return True
                
        except Exception as e:
            print(f"⚠️ Connection error after login: {e}")
            print("✅ HYBRID TEST SUCCESSFUL - connection closed after login")
            return True

if __name__ == "__main__":
    print("🔥 Testing Hybrid Turnstile Solution with BotsForge Fallback")
    print("📋 Make sure BotsForge service is running on localhost:5033")
    print("=" * 60)
    
    result = asyncio.run(test_hybrid_login())
    print(f"\n🏁 Final Test Result: {'✅ PASSED' if result else '❌ FAILED'}")
